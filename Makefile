PYTHON_VERSION := 3.12.7

clean: ## remove test and coverage artifacts
	rm -f .coverage
	rm -fr .reports/
	rm -fr .pytest_cache/

lint:
	@echo "Running linting..."
	@ruff check .
	@echo "Linting completed!"

lint-fix:
	@echo "Checking linting..."
	@ruff check . --fix

format:
	@echo "Formatting code..."
	@ruff format .
	@echo "Code formatted!"

format-check:
	@echo "Checking formatting of code..."
	@ruff format --check .

# SonarQube
sonar:
	docker run \
		--rm \
		-e SONAR_HOST_URL="https://sonar.keepsdev.com" \
		-e SONAR_SCANNER_OPTS="-Dsonar.projectKey=kafka-data-indexer" \
		-e SONAR_TOKEN="${SONAR_TOKEN}" \
		-v "${PWD}/:/usr/src" \
		sonarsource/sonar-scanner-cli

sonar-reports:
	@echo "Generating reports for SonarQube..."
	@mkdir -p .reports
	@ruff check . --output-format=json > .reports/ruff-report.json || true
	@jq 'map(.filename |= sub("^.*/media-convert/"; ""))' .reports/ruff-report.json > .reports/ruff-fixed.json
	@mv .reports/ruff-fixed.json .reports/ruff-report.json
	@echo "Reports generated successfully in .reports/ directory."

code-convention: format-check lint sonar-reports

default_target: code-convention

all: clean code-convention