# Database engines and queries
import os
import uuid
from collections.abc import Generator
from contextlib import contextmanager
from string import Template

from sqlalchemy import Engine, MappingResult, text
from sqlalchemy.engine import Connection

from .dev_postgres import engine as dev_postgres_engine  # noqa: F401

# Export all engines
from .konquest import engine as konquest_engine  # noqa: F401
from .kontent import engine as kontent_engine  # noqa: F401
from .myaccount import engine as myaccount_engine  # noqa: F401
from .regulatory_compliance import engine as regulatory_compliance  # noqa: F401
from .smartzap import engine as smartzap_engine  # noqa: F401


def format_query_values(values: dict):
    for arg_key in values:
        value = values[arg_key]
        if isinstance(value, list):
            value = value if len(value) > 0 else [uuid.uuid4()]
            values[arg_key] = ",".join(f"'{item}'" for item in value)
        elif value is None:
            values[arg_key] = "NULL"
        elif isinstance(value, str):
            value = value.replace("'", "''")
            values[arg_key] = f"'{value}'"

    return values


# Run a sql file
def run_sql(connection: Connection, file_path: str, **kwargs) -> MappingResult:
    """
    Run SQL statements from a template, replacing any variables specified in kwargs.

    :param connection: Database connection
    :param file_path: Location of the SQL file
    :param kwargs: any combination of key=value replaced in the SQL file

    :return: SQL execution response
    """
    file_message = f"{os.path.dirname(__file__)}/{file_path}"
    sql = Template(open(file_message, encoding="utf-8").read()).substitute(kwargs)
    rs = connection.execute(text(sql)).mappings()
    return rs


@contextmanager
def transaction_context(db_engine: Engine) -> Generator[Connection, None, None]:
    """
    Context manager that manages the database connection and transaction.
    - Connects to the database.
    - Starts the transaction with conn.begin().
    - Ensures commit or rollback of the transaction at the end of the 'with' block.

    Note: SQLAlchemy 2.0's conn.begin() automatically handles commit/rollback,
    so we don't need explicit transaction management.
    """
    with db_engine.connect() as conn:
        with conn.begin():
            yield conn
