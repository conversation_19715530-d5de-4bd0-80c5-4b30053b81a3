import os

from sqlalchemy import Engine, create_engine
from sqlalchemy.pool import Queue<PERSON><PERSON>


def get_environment_config() -> dict:
    env_suffix = os.getenv("ENV_SUFFIX", "dev").lower()
    app_name = os.getenv("APP_NAME", "kafka-data-indexer")

    if env_suffix in ["prod", "production"]:
        return {
            "pool_size": 2,
            "max_overflow": 3,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "connect_args": {
                "connect_timeout": 30,
                "application_name": app_name,
            },
        }

    return {
        "pool_size": 1,
        "max_overflow": 2,
        "pool_timeout": 15,
        "pool_recycle": 1800,
        "connect_args": {
            "connect_timeout": 10,
            "application_name": app_name,
        },
    }


def create_database_engine(db_name_env: str, url_env: str | None = None, **engine_kwargs) -> Engine:
    if url_env:
        db_url = os.getenv(url_env, "postgresql://dev-postgres")
    else:
        db_name = os.getenv(db_name_env)
        db_host = os.getenv("PG_HOSTNAME")
        db_port = os.getenv("PG_PORT", "5432")
        db_user = os.getenv("PG_USER")
        db_pass = os.getenv("PG_PASSWORD")
        db_url = f"postgresql://{db_user}:{db_pass}@{db_host}:{db_port}/{db_name}"

    env_config = get_environment_config()

    default_config = {
        "poolclass": QueuePool,
        "pool_pre_ping": True,
        "echo": False,
        "future": True,
    }

    default_config.update(env_config)
    default_config.update(engine_kwargs)

    return create_engine(db_url, **default_config)


def create_myaccount_engine() -> Engine:
    return create_database_engine("PG_DBNAME_MYACCOUNT")


def create_konquest_engine() -> Engine:
    return create_database_engine("PG_DBNAME_KONQUEST")


def create_kontent_engine() -> Engine:
    return create_database_engine("PG_DBNAME_KONTENT")


def create_smartzap_engine() -> Engine:
    return create_database_engine("PG_DBNAME_SMARTZAP")


def create_regulatory_compliance_engine() -> Engine:
    return create_database_engine("PG_DBNAME_REGULATORY_COMPLIANCE")


def create_dev_postgres_engine() -> Engine:
    return create_database_engine(db_name_env=None, url_env="DATABASE_DEV_POSTGRES_URL")
