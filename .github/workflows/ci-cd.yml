
name: CI/CD Pipeline

on:
  pull_request:
    branches:
      - develop
    types: [opened, synchronize, reopened]
  push:
    branches:
      - main
      - develop
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.12'
  ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}

jobs:
  lint:
    name: Code Linting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pip
            .venv
          key: ${{ runner.os }}-pip-${{ hashFiles('requirements-dev.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Create virtual environment
        run: python -m venv .venv

      - name: Activate virtual environment and install dependencies
        run: |
          source .venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Run Ruff linting
        run: |
          source .venv/bin/activate
          make lint

  format-check:
    name: Code Formatting Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pip
            .venv
          key: ${{ runner.os }}-pip-${{ hashFiles('requirements-dev.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Create virtual environment
        run: python -m venv .venv

      - name: Activate virtual environment and install dependencies
        run: |
          source .venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Run Ruff formatting check
        run: |
          source .venv/bin/activate
          make format-check

  sonar:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    needs: [lint, format-check]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pip
            .venv
          key: ${{ runner.os }}-pip-${{ hashFiles('requirements-dev.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Create virtual environment
        run: python -m venv .venv

      - name: Activate virtual environment and install dependencies
        run: |
          source .venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Generate SonarQube reports
        run: |
          source .venv/bin/activate
          make sonar-reports

      - name: Upload reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: sonar-reports
          path: .reports/
          include-hidden-files: true
          retention-days: 5

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: SonarQube Quality Gate Check
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  deploy:
    name: Deploy to EKS
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    needs: [lint, format-check, sonar]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_EKS }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_EKS }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Configure kubeconfig
        run: aws eks update-kubeconfig --region ${{ secrets.AWS_DEFAULT_REGION }} --name keeps-eks-cluster

      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ${{ secrets.AWS_DEFAULT_REGION }} | docker login --username AWS --password-stdin ${{ env.ECR_REPOSITORY }}

      - name: Build and Push Docker Image
        run: |
          if [[ "${GITHUB_REF}" == "refs/heads/main" ]]; then
              IMAGE_TAG="production"
              NAMESPACE="production"
          else
              IMAGE_TAG="stage"
              NAMESPACE="stage"
          fi

          docker build -t ${{ env.ECR_REPOSITORY }}:$IMAGE_TAG .
          docker push ${{ env.ECR_REPOSITORY }}:$IMAGE_TAG

      - name: Deploy to EKS
        run: |
          if [[ "${GITHUB_REF}" == "refs/heads/main" ]]; then
              NAMESPACE="production"
          else
              NAMESPACE="stage"
          fi

          # Deploy all kafka data indexer services
          kubectl rollout restart deployment/kafka-data-indexer -n $NAMESPACE
          kubectl rollout restart deployment/konquest-data-sync -n $NAMESPACE
          kubectl rollout restart deployment/smartzap-data-sync -n $NAMESPACE
          kubectl rollout restart deployment/regulatory-compliance-data-sync -n $NAMESPACE

          # Wait for deployments to complete
          kubectl rollout status deployment/kafka-data-indexer -n $NAMESPACE
          kubectl rollout status deployment/konquest-data-sync -n $NAMESPACE
          kubectl rollout status deployment/smartzap-data-sync -n $NAMESPACE
          kubectl rollout status deployment/regulatory-compliance-data-sync -n $NAMESPACE
