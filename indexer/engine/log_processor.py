from ..logging_config import get_logger
from .base_processor import BaseProcessor
from .keeps_message import KeepsMessage


class LogProcessor(BaseProcessor):
    def __init__(self, topics: list[str] = [], logMessages: bool = True) -> None:
        self.topics = topics
        self.logMessages = logMessages
        self.logger = get_logger("LogProcessor")

    def get_kafka_topics(self) -> list[str]:
        return self.topics

    def process_batch(self, batch: list[KeepsMessage]) -> bool:
        self.logger.info("-----------------------")
        self.logger.info(f"batch size: {len(batch)}")

        if self.logMessages:
            for i, msg in enumerate(batch, start=1):
                self.logger.info("")
                self.logger.info(f"message: {i}")
                msg.log()

        self.logger.info("")
        return True
