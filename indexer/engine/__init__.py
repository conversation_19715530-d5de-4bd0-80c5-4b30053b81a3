# Módulo proposto com base mais genérica de processadores de mensagens

# TODO:
# - ajustar worker para usar processadores que estendem estas classes
# - internalização inicialização de indices no processor base do elastic
# - criar processador base para banco


from .base_elastic_processor import BaseElasticProcessor
from .base_processor import BaseProcessor
from .keeps_message import KeepsMessage
from .log_processor import LogProcessor


# Export base structure
BaseProcessor, BaseElasticProcessor, KeepsMessage, LogProcessor
