import abc
import typing  # python 3.8<

from cachetools import TTL<PERSON>ache

import db
import es

from . import BaseProcessor
from .keeps_message import KeepsMessage


DELETED_CACHE_SIZE = 10000
DELETED_CACHE_TTL = 60 * 60  # 1 hour


# Base structure of a Elastic Processor
class BaseElasticProcessor(BaseProcessor):
    def __init__(self):
        super().__init__()
        self.db = db
        self.es = es
        self._elastic_index = self.get_index_name()
        self._kafka_main_topic = self.get_kafka_main_topic()
        self._kafka_sub_topics = self.get_kafka_sub_topics()
        self._cache_deleted = TTLCache(maxsize=DELETED_CACHE_SIZE, ttl=DELETED_CACHE_TTL)

    @abc.abstractmethod
    def get_kafka_main_topic(self) -> str:
        pass

    @abc.abstractmethod
    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        pass

    def get_kafka_topics(self) -> list[str]:
        topics = []
        if self._kafka_main_topic:
            topics.append(self._kafka_main_topic)
        if self._kafka_sub_topics:
            topics.extend(self._kafka_sub_topics.keys())
        return topics

    @abc.abstractmethod
    def get_index_name(self) -> str:
        pass

    def get_index_settings(self) -> str:
        return {
            "analysis": {
                "normalizer": {"case_insensitive": {"filter": ["lowercase"]}},
                "analyzer": {"folding": {"tokenizer": "standard", "filter": ["lowercase", "asciifolding"]}},
            }
        }

    @abc.abstractmethod
    def get_index_mappings(self) -> str:
        pass

    def get_deleted_ids(self) -> list[str]:
        return self._cache_deleted.keys()

    def process_batch(self, batch: list[KeepsMessage]) -> bool:
        updated_ids, deleted_ids = self.pre_process_batch(batch)

        self.log(f"-- {len(batch)} messages, {len(updated_ids)} updated and {len(deleted_ids)} deleted.")

        if deleted_ids:
            self.log("-- Removing deleted docs...")
            self.es.bulk_delete(self._elastic_index, deleted_ids)

        if not updated_ids:
            self.log("-- No docs for processing...")
            return True

        self.log("-- Processing...")
        return self.do_process_batch(batch, updated_ids, deleted_ids)

    # Pre-processing for main document.
    def pre_process_batch(self, batch: list[KeepsMessage]) -> tuple:
        delete_msgs = [msg for msg in batch if msg.topic == self._kafka_main_topic and msg.op == "d"]
        update_msgs = [msg for msg in batch if msg.topic != self._kafka_main_topic or msg.op != "d"]

        logical_deleted_msgs = [msg for msg in batch if msg.topic == self._kafka_main_topic and msg.op != "d" and msg.entity("deleted")]

        deleted_ids = self.extract_ids(delete_msgs)
        deleted_ids.update(self.extract_ids(logical_deleted_msgs))
        for id in deleted_ids:
            self._cache_deleted[id] = True

        updated_ids = self.extract_ids(update_msgs, self._kafka_sub_topics)
        updated_ids.difference_update(deleted_ids)
        updated_ids.difference_update(self._cache_deleted.keys())

        return updated_ids, deleted_ids

    @abc.abstractmethod
    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str], deleted_ids: set[str]) -> bool:
        pass
