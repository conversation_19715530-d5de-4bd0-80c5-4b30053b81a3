import math
import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class EnrollmentProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-enrollments-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.mission_enrollment"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return {
            f"{self.TOPIC_PREFIX}.answer": lambda msg: msg.entity("enrollment_id"),
        }

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            "properties": {
                "id": {"type": "keyword"},
                "give_up": {"type": "boolean"},
                "give_up_comment": {"type": "text"},
                "status": {"type": "keyword"},
                "required": {"type": "boolean"},
                "points": {
                    "type": "scaled_float",
                    "scaling_factor": 100,
                },
                "performance": {
                    "type": "scaled_float",
                    "scaling_factor": 100,
                },
                "progress": {
                    "type": "scaled_float",
                    "scaling_factor": 100,
                },
                "goal_date": {"type": "date"},
                "start_date": {"type": "date"},
                "end_date": {"type": "date"},
                "created_date": {"type": "date"},
                "updated_date": {"type": "date"},
                "workspace_id": {"type": "keyword"},
                "course_id": {"type": "keyword"},
                "course_name": {
                    "type": "text",
                    "copy_to": "search_terms",
                    "fields": {"keyword": {"type": "keyword"}, "sortable": {"type": "keyword", "normalizer": "case_insensitive"}},
                },
                "category_id": {"type": "keyword"},
                "category_name": {
                    "type": "text",
                    "copy_to": "search_terms",
                    "fields": {"keyword": {"type": "keyword"}, "sortable": {"type": "keyword", "normalizer": "case_insensitive"}},
                },
                "user_id": {"type": "keyword"},
                "user_name": {
                    "type": "text",
                    "copy_to": "search_terms",
                    "fields": {
                        "keyword": {"type": "keyword"},
                        "sortable": {"type": "keyword", "normalizer": "case_insensitive"},
                    },
                },
                "user_avatar": {"type": "keyword"},
                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },
                "questions": {
                    "type": "object",
                    "properties": {
                        "questions_total": {"type": "keyword"},
                        "questions_answered": {"type": "integer"},
                        "questions_answered_ratio": {
                            "type": "scaled_float",
                            "scaling_factor": 100,
                        },
                        "questions_answered_correctly": {"type": "integer"},
                        "questions_answered_correctly_ratio": {
                            "type": "scaled_float",
                            "scaling_factor": 100,
                        },
                    },
                },
            }
        }

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        with self.db.konquest_engine.connect() as konquest_conn:
            docs = []

            in_ids = ",".join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, "konquest/enrollments_chunk.sql", enrollments_ids=in_ids)

            for i, row in enumerate(rs):
                enrollment_id = self.str_id(row["id"])
                self.log(f"[{enrollment_id}] -- Processing {i + 1} of {len(updated_ids)}")

                questions = self._compute_questions(row)

                points = row["points"]
                performance = row["performance"]

                if isinstance(points, float) and math.isnan(points):
                    points = 0
                if isinstance(performance, float) and math.isnan(performance):
                    performance = 0

                enrollments = {
                    "_id": enrollment_id,  # elastic index id
                    "id": enrollment_id,
                    "status": row["status"],
                    "required": row["required"],
                    "points": points,
                    "performance": performance,
                    "progress": row["progress"],
                    "give_up": row["give_up"],
                    "give_up_comment": row["give_up_comment"],
                    "user_id": self.str_id(row["user_id"]),
                    "user_name": row["user_name"],
                    "user_avatar": row["user_avatar"],
                    "workspace_id": self.str_id(row["workspace_id"]),
                    "course_id": self.str_id(row["mission_id"]),
                    "course_name": row["mission_name"],
                    "category_id": self.str_id(row["category_id"]),
                    "category_name": row["category_name"],
                    "goal_date": self.str_date(row["goal_date"]),
                    "start_date": self.str_date(row["start_date"]),
                    "end_date": self.str_date(row["end_date"]),
                    "created_date": self.str_date(row["created_date"]),
                    "updated_date": self.str_date(row["updated_date"]),
                    "questions": questions,
                }

                docs.append(enrollments)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True

    def _compute_questions(self, row):
        total = row["questions_total"]
        answered = row["questions_answered"]
        correct = row["questions_answered_correctly"]

        questions = {
            "questions_total": total,
            "questions_answered": answered,
            "questions_answered_ratio": answered / total if total else 0,
            "questions_answered_correctly": correct,
            "questions_answered_correctly_ratio": correct / total if total else 0,
        }
        return questions
