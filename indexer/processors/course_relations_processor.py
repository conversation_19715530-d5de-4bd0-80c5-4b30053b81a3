import os
import typing

from indexer.constants import KAFKA_DELETED_OPERATION

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class CourseRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-courses-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"
    USER_TOPIC = f"pg_myaccount_{ENV_SUFFIX}.public.user"
    WORKSPACE_TOPIC = f"pg_myaccount_{ENV_SUFFIX}.public.workspace"

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            "mission_category",
            "mission_type",
            "mission_contributor",
            "mission_workspace",
            "mission_tag",
            "external_mission",
            "mission_provider",
            "presential_mission",
            "live_mission",
        ]
        topics = {f"{self.TOPIC_PREFIX}.{table}": None for table in tables}
        topics[self.USER_TOPIC] = None
        topics[self.WORKSPACE_TOPIC] = None
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: list[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        # updated related messages for many/one to one relationship and myaccount tables
        updated_msgs = [msg for msg in batch if msg.op not in ["d", "r"]]

        categories = [category for category in updated_msgs if category.topic == self.TOPIC_PREFIX + ".mission_category"]
        types = [type for type in updated_msgs if type.topic == self.TOPIC_PREFIX + ".mission_type"]
        live_missions = [lm for lm in updated_msgs if lm.topic == self.TOPIC_PREFIX + ".live_mission"]
        presential_missions = [pm for pm in updated_msgs if pm.topic == self.TOPIC_PREFIX + ".presential_mission"]
        external_missions = [em for em in updated_msgs if em.topic == self.TOPIC_PREFIX + ".external_mission"]
        mission_providers = [mp for mp in updated_msgs if mp.topic == self.TOPIC_PREFIX + ".mission_provider"]

        myaccount_users = [user for user in updated_msgs if user.topic == self.USER_TOPIC]
        myaccount_workspaces = [workspace for workspace in updated_msgs if workspace.topic == self.WORKSPACE_TOPIC]

        # updated and deleted messages for many to many relationships
        updated_or_deleted_msgs = [msg for msg in batch if msg.op not in ["r"]]

        mission_contributors = [contributor for contributor in updated_or_deleted_msgs if contributor.topic == self.TOPIC_PREFIX + ".mission_contributor"]
        mission_workspaces = [workspace for workspace in updated_or_deleted_msgs if workspace.topic == self.TOPIC_PREFIX + ".mission_workspace"]
        deleted_mission_workspaces = [message for message in mission_workspaces if message.op == KAFKA_DELETED_OPERATION]
        mission_tags = [tag for tag in updated_or_deleted_msgs if tag.topic == self.TOPIC_PREFIX + ".mission_tag"]

        self.log(
            f"-- {len(batch)} messages, {len(categories)} categories, {len(types)} types, "
            f"{len(live_missions)} live_missions, {len(presential_missions)} presential_missions, "
            f"{len(external_missions)} external_missions, {len(mission_providers)} mission_providers, "
            f"{len(myaccount_users)} users, {len(mission_contributors)} contributors, "
            f"{len(mission_workspaces)} workspaces, {len(mission_tags)} tags"
        )

        for category in categories:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"course_category.id": category.id}},
                    "script": {"lang": "painless", "source": 'ctx._source.course_category.name = params["name"]', "params": {"name": category.entity("name")}},
                },
            )

        for type in types:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"course_type.id": type.id}},
                    "script": {"lang": "painless", "source": 'ctx._source.course_type.name = params["name"]', "params": {"name": type.entity("name")}},
                },
            )

        for lm in live_missions:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"live_mission.id": lm.id}},
                    "script": {
                        "lang": "painless",
                        "source": """
                        ctx._source.live_mission.url = params['url'];
                        ctx._source.live_mission.seats = params['seats'];
                        ctx._source.live_mission.remaining_seats = params['remaining_seats'];
                        ctx._source.live_mission.notify_users_enrolled = params['notify_users_enrolled'];
                        ctx._source.live_mission.allow_any_enrollment = params['allow_any_enrollment'];
                        """.replace("\n", ""),
                        "params": {
                            "url": lm.entity("url"),
                            "seats": lm.entity("seats"),
                            "remaining_seats": lm.entity("remaining_seats"),
                            "notify_users_enrolled": lm.entity("notify_users_enrolled"),
                            "allow_any_enrollment": lm.entity("allow_any_enrollment"),
                        },
                    },
                },
            )

        for pm in presential_missions:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"presential_mission.id": pm.id}},
                    "script": {
                        "lang": "painless",
                        "source": """
                        ctx._source.presential_mission.address = params['address'];
                        ctx._source.presential_mission.seats = params['seats'];
                        ctx._source.presential_mission.remaining_seats = params['remaining_seats'];
                        ctx._source.presential_mission.notify_users_enrolled = params['notify_users_enrolled'];
                        ctx._source.presential_mission.allow_any_enrollment = params['allow_any_enrollment'];
                        """.replace("\n", ""),
                        "params": {
                            "address": pm.entity("address"),
                            "seats": pm.entity("seats"),
                            "remaining_seats": pm.entity("remaining_seats"),
                            "notify_users_enrolled": pm.entity("notify_users_enrolled"),
                            "allow_any_enrollment": pm.entity("allow_any_enrollment"),
                        },
                    },
                },
            )

        for em in external_missions:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"external_mission.id": em.id}},
                    "script": {
                        "lang": "painless",
                        "source": """
                        ctx._source.external_mission.course_type = params['course_type'];
                        ctx._source.external_mission.course_url = params['course_url'];
                        """.replace("\n", ""),
                        "params": {
                            "course_type": em.entity("course_type"),
                            "course_url": em.entity("course_url"),
                        },
                    },
                },
            )

        for mp in mission_providers:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"external_mission.provider_id": mp.id}},
                    "script": {
                        "lang": "painless",
                        "source": """
                        ctx._source.external_mission.provider_name = params['name'];
                        ctx._source.external_mission.provider_description = params['description'];
                        ctx._source.external_mission.provider_icon = params['icon'];
                        """.replace("\n", ""),
                        "params": {
                            "name": mp.entity("name"),
                            "description": mp.entity("description"),
                            "icon": mp.entity("icon"),
                        },
                    },
                },
            )

        # MyAccount user
        for user in myaccount_users:
            q = {
                "query": {"term": {"user_creator.id": user.id}},
                "script": {
                    "lang": "painless",
                    "source": """
                        ctx._source.user_creator = [
                            'id': params['id'],
                            'name': params['name'],
                            'status': params['status'],
                            'email': params['email'],
                            'avatar': params['avatar']
                        ]
                        """.replace("\n", ""),
                    "params": {
                        "id": user.id,
                        "name": user.entity("name"),
                        "status": user.entity("status"),
                        "email": user.entity("email"),
                        "avatar": user.entity("avatar"),
                    },
                },
            }
            # print(f'--- query: {q}')
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts="proceed", body=q)

        # Contributors
        with self.db.konquest_engine.connect() as konquest_conn:
            affected_missions_ids = self.extract_ids(mission_contributors, {self.TOPIC_PREFIX + ".mission_contributor": lambda msg: msg.entity("mission_id")})
            for mission_id in affected_missions_ids:
                mission_contributors = self._query_mission_contributors(konquest_conn, mission_id)
                self.log(f'Updating contributors for course "{mission_id}" with {len(mission_contributors)} contributors')

                self.es.es_client.update_by_query(
                    index=self.ELASTIC_INDEX,
                    conflicts="proceed",
                    body={
                        "query": {"term": {"id": mission_id}},
                        "script": {
                            "lang": "painless",
                            "source": 'ctx._source.contributors = params["contributors"]',
                            "params": {"contributors": mission_contributors},
                        },
                    },
                )

        # Workspaces
        with self.db.konquest_engine.connect() as konquest_conn:
            # Deleted Mission Workspace
            deleted_relations_ids = self.extract_ids(deleted_mission_workspaces, {self.TOPIC_PREFIX + ".mission_workspace": lambda msg: msg.entity("id")})

            # Mission Workspace
            affected_missions_ids = self.extract_ids(mission_workspaces, {self.TOPIC_PREFIX + ".mission_workspace": lambda msg: msg.entity("mission_id")})

            # MyAccount Workspace
            workspace_ids = [workspace.id for workspace in myaccount_workspaces]
            if workspace_ids:
                mission_ids = self._query_course_ids_from_workspaces(konquest_conn, workspace_ids)
                affected_missions_ids.update(mission_ids)

            for mission_id in affected_missions_ids:
                mission_workspaces = self._query_course_workspaces(konquest_conn, mission_id)
                self.log(f'Updating workspaces for course "{mission_id}" with {len(mission_workspaces)} workspaces')

                self.es.es_client.update_by_query(
                    index=self.ELASTIC_INDEX,
                    conflicts="proceed",
                    body={
                        "query": {"term": {"id": mission_id}},
                        "script": {"lang": "painless", "source": 'ctx._source.workspaces = params["workspaces"]', "params": {"workspaces": mission_workspaces}},
                    },
                )

            for relation_id in deleted_relations_ids:
                self.es.es_client.update_by_query(
                    index=self.ELASTIC_INDEX,
                    conflicts="proceed",
                    body={
                        "query": {"nested": {"path": "workspaces", "query": {"bool": {"must": [{"term": {"workspaces.id": relation_id}}]}}}},
                        "script": {
                            "source": "ctx._source.workspaces.removeIf(workspace -> workspace.id == params.relationId)",
                            "lang": "painless",
                            "params": {"relationId": relation_id},
                        },
                    },
                )

        # Tags
        with self.db.konquest_engine.connect() as konquest_conn:
            affected_missions_ids = self.extract_ids(mission_tags, {self.TOPIC_PREFIX + ".mission_tag": lambda msg: msg.entity("mission_id")})
            for mission_id in affected_missions_ids:
                mission_tags = self._query_course_tags(konquest_conn, mission_id)
                self.log(f'Updating tags for course "{mission_id}" with {len(mission_tags)} tags')

                self.es.es_client.update_by_query(
                    index=self.ELASTIC_INDEX,
                    conflicts="proceed",
                    body={
                        "query": {"term": {"id": mission_id}},
                        "script": {"lang": "painless", "source": 'ctx._source.tags = params["tags"]', "params": {"tags": mission_tags}},
                    },
                )

        return True

    def _query_mission_contributors(self, konquest_conn, mission_id):
        contributors = []

        rs = self.db.run_sql(konquest_conn, "konquest/mission_contributors.sql", mission_id=mission_id)
        for row in rs:
            contributors.append(
                {
                    "relation_id": self.str_id(row["id"]),
                    "user_id": self.str_id(row["user_id"]),
                    "created_date": self.str_date(row["created_date"]),
                    "updated_date": self.str_date(row["updated_date"]),
                }
            )
        return contributors

    def _query_course_ids_from_workspaces(self, konquest_conn, workspace_ids):
        in_ids = ",".join([f"'{id}'" for id in workspace_ids])
        rs = self.db.run_sql(konquest_conn, "konquest/missions_from_workspaces.sql", workspace_ids=in_ids)
        missions_ids = [row["mission_id"] for row in rs]
        return missions_ids

    def _query_course_workspaces(self, konquest_conn, mission_id):
        workspaces = []

        rs = self.db.run_sql(konquest_conn, "konquest/mission_workspaces.sql", mission_id=mission_id)
        for row in rs:
            workspaces.append(
                {
                    "id": self.str_id(row["id"]),
                    "created_date": self.str_date(row["created_date"]),
                    "updated_date": self.str_date(row["updated_date"]),
                    "workspace_id": self.str_id(row["workspace_id"]),
                    "relationship_type": row["relationship_type"],
                    "minimum_performance": row["min_performance_certificate"],
                }
            )
        return workspaces

    def _query_course_tags(self, konquest_conn, mission_id):
        tags = []

        rs = self.db.run_sql(konquest_conn, "konquest/mission_tags.sql", mission_id=mission_id)
        for row in rs:
            tags.append(
                {
                    "id": self.str_id(row["id"]),
                    "name": row["name"],
                    "relevance": row["relevance"],
                }
            )
        return tags
