import os
import typing
import uuid

from db import transaction_context
from indexer.processors.smartzap.user_processor import UserProcessor

from .. import AbstractProcessor, KeepsMessage
from .service_workspace_processor import ServiceWorkspaceProcessor


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class UserWorkspaceProcessor(AbstractProcessor):
    TOPIC_PREFIX = f"pg_myaccount_{ENV_SUFFIX}.public"
    USER_WORKSPACE = "user_role_workspace"

    def __init__(
        self,
        user_processor: UserProcessor,
        workspace_processor: ServiceWorkspaceProcessor,
        smartzap_application_id: str,
        smartzap_service_id: str,
        smartzap_role_ids: list[str],
    ):
        super().__init__()
        self._user_processor = user_processor
        self._workspace_processor = workspace_processor
        self._smartzap_application_id = smartzap_application_id
        self._smartzap_service_id = smartzap_service_id
        self._smartzap_role_ids = smartzap_role_ids

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.user_role_workspace"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return ""

    def get_index_mappings(self) -> str:
        return ""

    def pre_process_batch(self, batch: list[KeepsMessage]):
        filtered_smartzap_batch = [msg for msg in batch if msg.size and msg.entity("role_id") in self._smartzap_role_ids]
        self.log(f"-- {len(filtered_smartzap_batch)} messages after filtering by role_id Smartzap")
        return super().pre_process_batch(filtered_smartzap_batch)

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        sanitized_ids = {uid.strip('"') for uid in updated_ids}
        filtered_batch = [msg for msg in batch if msg.entity("id") in sanitized_ids]

        user_extractor = {self.get_kafka_main_topic(): lambda msg: msg.entity("user_id")}
        workspace_extractor = {self.get_kafka_main_topic(): lambda msg: msg.entity("workspace_id")}

        user_ids = self.extract_ids(filtered_batch, user_extractor)
        workspace_ids = self.extract_ids(filtered_batch, workspace_extractor)

        self._process_users(user_ids)
        self._process_workspaces(workspace_ids)

        self._persist_user_workspace_relations(filtered_batch)
        return True

    def remove_deleted_instances(self, _: set[str], delete_msgs: list[KeepsMessage] = None):
        if delete_msgs is None:
            delete_msgs = []

        with self.db.myaccount_engine.connect() as myaccount_conn, transaction_context(self.db.smartzap_engine) as smartzap_conn:
            try:
                with smartzap_conn.begin():
                    for msg in delete_msgs:
                        self._remove_if_no_roles_on_myaccount(myaccount_conn, smartzap_conn, msg)
            except Exception as e:
                self.log(f"Error deleting user workspace relations: {e!s}")
                raise

    ########################################################
    # Private methods
    ########################################################
    def _process_users(self, user_ids):
        if user_ids:
            self._user_processor.do_process_batch([], user_ids)

    def _process_workspaces(self, workspace_ids):
        if workspace_ids:
            self._workspace_processor.do_process_batch([], workspace_ids, set())

    def _persist_user_workspace_relations(self, filtered_batch: list[KeepsMessage]):
        if not filtered_batch:
            return
        with transaction_context(self.db.smartzap_engine) as smartzap_conn:
            for msg in filtered_batch:
                self.log(f'Processing user "{msg.entity("user_id")}"')
                self._save_user_workspace_relation(smartzap_conn, msg)

    def _save_user_workspace_relation(self, conn, msg: KeepsMessage):
        self.log(f'Creating/Updating user workspace relation for user "{msg.entity("user_id")}"')
        values = self.db.format_query_values(
            {
                "id": str(uuid.uuid4()),
                "user_id": msg.entity("user_id"),
                "workspace_id": msg.entity("workspace_id"),
                "created": msg.entity("created_date"),
            }
        )
        query_file_path = "smartzap/smartzap_users_workspace_save.sql"
        self.db.run_sql(conn, query_file_path, **values)

    def _remove_if_no_roles_on_myaccount(self, myaccount_conn, smartzap_conn, msg: KeepsMessage):
        user_id = msg.entity("user_id")
        workspace_id = msg.entity("workspace_id")

        if not user_id or not workspace_id:
            return

        remaining_roles_count = self._get_remaining_roles(myaccount_conn, user_id, workspace_id)

        if remaining_roles_count == 0:
            query_file_path = "smartzap/smartzap_users_workspace_delete.sql"
            self.db.run_sql(smartzap_conn, query_file_path, user_id=user_id, workspace_id=workspace_id)
            self.log(f'Relation deleted for user "{user_id}" and workspace "{workspace_id}"')

    def _get_remaining_roles(self, conn, user_id: str, workspace_id: str) -> int:
        query_file_path = "myaccount/check_user_smartzap_roles.sql"
        result = self.db.run_sql(conn, query_file_path, user_id=user_id, workspace_id=workspace_id, smartzap_application_id=self._smartzap_application_id)
        return next((row["count"] for row in result), 0)
