import os
import random
import re
import string
import typing

from db import format_query_values, transaction_context

from .. import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class UserProcessor(AbstractProcessor):
    TOPIC_PREFIX = f"pg_myaccount_{ENV_SUFFIX}.public"

    def __init__(self, smartzap_application_id: str):
        super().__init__()
        self._smartzap_application_id = smartzap_application_id

    def get_index_name(self) -> str:
        pass

    def get_index_mappings(self) -> str:
        pass

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.user"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return {
            f"{self.TOPIC_PREFIX}.user_profile_workspace": lambda msg: msg.entity("user_id"),
        }

    def pre_process_batch(self, batch: list[KeepsMessage]):
        update_msgs = [msg for msg in batch if msg.op != "d"]
        updated_ids = self.extract_ids(
            update_msgs,
            {
                self._kafka_main_topic: lambda msg: msg.entity("id"),
            },
        )

        self.log(f"-- {len(batch)} messages, {len(updated_ids)} updated")

        if not updated_ids:
            self.log("-- No docs for processing...")
            return True

        self.log("-- Processing...")
        return self.do_process_batch(batch, updated_ids)

    def normalize_brazilian_9_digits(self, phone: str) -> str:
        phone = re.sub(r"\D", "", phone)
        if phone.startswith("55"):
            # Remove leading '0' in DDD if present
            phone = phone[:2] + phone[3:] if phone[2] == "0" else phone

            # Add '9' for mobile numbers if necessary
            if len(phone) == 12 and phone[4] in "789":
                phone = phone[:4] + "9" + phone[4:]

        return phone

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        try:
            objects_to_save = self._fetch_and_transform_data(updated_ids)
            self._save_transformed_data(objects_to_save)
            return True
        except Exception as e:
            self.log(f"Error in processing batch: {e!s}")
            raise e

    def _fetch_and_transform_data(self, updated_ids: set[str]) -> list[dict]:
        """
        Fetch user data from the database and transform rows into objects to save.
        """
        objects_to_save = []

        in_ids = ",".join([f"'{id}'" for id in updated_ids])
        with self.db.myaccount_engine.connect() as account_conn:
            rs = self.db.run_sql(account_conn, "myaccount/smartzap_users_chunk.sql", users_ids=in_ids, smartzap_application_id=self._smartzap_application_id)

            for i, row in enumerate(rs):
                object_id = self.str_id(row["id"])
                self.log(f"[{object_id}] -- Processing {i + 1} of {len(updated_ids)}")
                objects_to_save.append(self._transform_row(dict(row)))

        return objects_to_save

    def _save_transformed_data(self, objects_to_save: list[dict]) -> None:
        """
        Save transformed user data into the destination database, handling conflicts.
        """
        with transaction_context(self.db.smartzap_engine) as conn:
            for object_to_save in objects_to_save:
                self._process_single_object(conn, object_to_save)

    def _process_single_object(self, conn, object_to_save: dict) -> None:
        object_to_save = format_query_values(object_to_save)

        self._resolve_phone_conflict(conn, object_to_save)
        self._resolve_email_conflict(conn, object_to_save)
        self._insert_or_update_user(conn, object_to_save)

    def _resolve_phone_conflict(self, conn, object_to_save: dict) -> None:
        self.db.run_sql(
            conn,
            "smartzap/smartzap_users_phone_conflict_update.sql",
            phone=object_to_save["phone"],
            id=object_to_save["id"],
            temp_phone=object_to_save["temp_phone"],
            name=object_to_save["name"],
        )

    def _resolve_email_conflict(self, conn, object_to_save: dict) -> None:
        self.db.run_sql(conn, "smartzap/smartzap_users_email_conflict_update.sql", email=object_to_save["email"], id=object_to_save["id"])

    def _insert_or_update_user(self, conn, object_to_save: dict) -> None:
        self.db.run_sql(conn, "smartzap/smartzap_users_save.sql", **object_to_save)

    def format_cpf(self, cpf: str) -> str | None:
        if not cpf:
            return None
        cpf = re.sub(r"\.0$", "", cpf)
        return re.sub(r"\D", "", cpf)

    def _transform_row(self, row: dict) -> dict:
        phone = row.get("phone")
        temp_phone = "#" + "".join(random.choice(string.digits) for _ in range(12))
        sync_check = "phone_ok"
        if not phone:
            phone = temp_phone
            sync_check = "phone_not_reported"

        return {
            "id": self.str_id(row["id"]),
            "name": row.get("name"),
            "temp_phone": temp_phone,
            "nickname": row.get("nickname"),
            "email": row.get("email"),
            "ein": row.get("ein"),
            "secondary_email": row.get("secondary_email"),
            "cpf": self.format_cpf(row.get("cpf")),
            "director": row.get("director"),
            "manager": row.get("manager"),
            "area_of_activity": row.get("area_of_activity"),
            "job": row.get("job"),
            "phone": self.normalize_brazilian_9_digits(phone),
            "gender": row.get("gender"),
            "birthday": self.str_date(row.get("birthday")),
            "address": row.get("address"),
            "avatar": row.get("avatar"),
            "status": row.get("status"),
            "sync_check": sync_check,
            "created": self.str_date(row.get("created_date")),
            "updated": self.str_date(row.get("updated_date")),
            "language_id": self.str_id(row.get("language_id")),
            "workspace_id": self.str_id(row.get("workspace_id")),
        }
