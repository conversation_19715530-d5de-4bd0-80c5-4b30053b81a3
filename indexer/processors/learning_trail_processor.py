import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class LearningTrailProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-learning-trails-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.learning_trail"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return {
            f"{self.TOPIC_PREFIX}.learning_trail_step": lambda msg: msg.entity("learning_trail_id"),
        }

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            "properties": {
                "id": {"type": "keyword"},
                "name": {
                    "type": "text",
                    "fields": {
                        "keyword": {"type": "keyword"},
                        "sortable": {"type": "keyword", "normalizer": "case_insensitive"},
                    },
                },
                "description": {"type": "text", "copy_to": "search_terms"},
                "duration_time": {"type": "integer"},
                "points": {"type": "integer"},
                "is_active": {"type": "boolean"},
                "thumb_image": {"type": "keyword"},
                "holder_image": {"type": "keyword"},
                "language": {"type": "keyword"},
                "created_date": {"type": "date"},
                "updated_date": {"type": "date"},
                "expiration_date": {"type": "date"},
                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },
                "workspaces": {
                    "type": "nested",
                    "properties": {
                        "id": {"type": "keyword"},
                        "workspace_id": {"type": "keyword"},
                        "relationship_type": {"type": "keyword"},
                    },
                },
                "user_creator": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "keyword"},
                        "avatar": {"type": "keyword"},
                        "name": {
                            "type": "text",
                            "copy_to": "user_creator.search_terms",
                            "fields": {
                                "keyword": {"type": "keyword"},
                                "sortable": {"type": "keyword", "normalizer": "case_insensitive"},
                            },
                        },
                        "search_terms": {
                            "type": "search_as_you_type",
                            "analyzer": "folding",
                        },
                    },
                },
                "trail_type": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "keyword"},
                        "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    },
                },
                "trail_steps": {
                    "type": "nested",
                    "properties": {
                        "step_id": {"type": "keyword"},
                        "step_order": {"type": "integer"},
                        "step_name": {"type": "keyword"},
                        "mission_id": {"type": "keyword"},
                        "mission_name": {
                            "type": "text",
                            "copy_to": "trail_steps.search_terms",
                            "fields": {
                                "keyword": {"type": "keyword"},
                            },
                        },
                        "mission_category_id": {"type": "keyword"},
                        "mission_category_name": {
                            "type": "text",
                            "fields": {
                                "keyword": {"type": "keyword"},
                            },
                        },
                        "pulse_id": {"type": "keyword"},
                        "pulse_name": {
                            "type": "text",
                            "copy_to": "trail_steps.search_terms",
                            "fields": {
                                "keyword": {"type": "keyword"},
                            },
                        },
                        "pulse_channels": {
                            "type": "nested",
                            "properties": {
                                "id": {"type": "keyword"},
                                "name": {"type": "text", "copy_to": "trail_steps.search_terms", "fields": {"keyword": {"type": "keyword"}}},
                                "description": {
                                    "type": "text",
                                    "copy_to": "trail_steps.search_terms",
                                },
                                "category_id": {"type": "keyword"},
                                "category_name": {
                                    "type": "text",
                                    "copy_to": "trail_steps.search_terms",
                                    "fields": {
                                        "keyword": {"type": "keyword"},
                                    },
                                },
                                "workspace_id": {"type": "keyword"},
                            },
                        },
                        "search_terms": {
                            "type": "search_as_you_type",
                            "analyzer": "folding",
                        },
                    },
                },
            }
        }

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        with self.db.konquest_engine.connect() as konquest_conn:
            docs = []

            in_ids = ",".join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, "konquest/learning_trails_chunk.sql", ids=in_ids)

            for i, row in enumerate(rs):
                trail_id = self.str_id(row["id"])
                self.log(f"[{trail_id}] -- Processing {i + 1} of {len(updated_ids)}")

                # Many/One to One

                _type = {"id": self.str_id(row["learning_trail_type_id"]), "name": row["learning_trail_type_name"]}

                _user_creator = {
                    "id": self.str_id(row["user_creator_id"]),
                    "name": row["user_creator_name"],
                    "avatar": row["user_creator_avatar"],
                }

                # One to Many

                _workspaces = self._query_trail_workspaces(konquest_conn, trail_id)

                _steps = self._query_trail_steps(konquest_conn, trail_id)

                # The doc
                trail = {
                    "_id": trail_id,  # elastic index id
                    "id": trail_id,
                    "name": row["name"],
                    "description": row["description"],
                    "duration_time": row["duration_time"],
                    "points": row["points"],
                    "is_active": row["is_active"],
                    "thumb_image": row["thumb_image"],
                    "holder_image": row["holder_image"],
                    "language": row["language"],
                    "created_date": self.str_date(row["created_date"]),
                    "updated_date": self.str_date(row["updated_date"]),
                    "expiration_date": self.str_date(row["expiration_date"]),
                    "workspaces": _workspaces,
                    "user_creator": _user_creator,
                    "trail_type": _type,
                    "trail_steps": _steps,
                }
                docs.append(trail)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True

    def _query_trail_workspaces(self, konquest_conn, trail_id):
        workspaces = []

        rs = self.db.run_sql(konquest_conn, "konquest/learning_trail_workspaces.sql", learning_trail_id=trail_id)
        for row in rs:
            workspace = {
                "id": self.str_id(row["id"]),
                "workspace_id": self.str_id(row["workspace_id"]),
                "relationship_type": row["relationship_type"],
            }
            workspaces.append(workspace)
        return workspaces

    def _query_trail_steps(self, konquest_conn, trail_id):
        steps = []

        rs = self.db.run_sql(konquest_conn, "konquest/learning_trail_steps.sql", learning_trail_id=trail_id)
        for row in rs:
            pulse_id = row["pulse_id"]
            channels = self._query_pulse_channels(konquest_conn, pulse_id) if pulse_id else None

            step = {
                "step_id": self.str_id(row["step_id"]),
                "step_order": row["step_order"],
                "mission_id": self.str_id(row["mission_id"]),
                "mission_name": row["mission_name"],
                "mission_category_id": self.str_id(row["mission_category_id"]),
                "mission_category_name": row["mission_category_name"],
                "pulse_id": self.str_id(row["pulse_id"]),
                "pulse_name": row["pulse_name"],
                "pulse_channels": channels,
            }
            steps.append(step)

        return steps

    def _query_pulse_channels(self, konquest_conn, pulse_id):
        channels = []

        rs = self.db.run_sql(konquest_conn, "konquest/pulse_channels.sql", pulse_id=pulse_id)
        for row in rs:
            channel = {
                "id": self.str_id(row["channel_id"]),
                "name": self.str_id(row["channel_name"]),
                "description": self.str_id(row["channel_description"]),
                "category_id": self.str_id(row["channel_category_id"]),
                "category_name": row["channel_category_name"],
                "workspace_id": self.str_id(row["workspace_id"]),
            }
            channels.append(channel)
        return channels
