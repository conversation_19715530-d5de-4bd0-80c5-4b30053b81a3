import os

from sqlalchemy.engine import Connection, ResultProxy, Row

from .abstract_learning_object_processor import AbstractLearningObjectProcessor


TRAIL_LEARNING_OBJECT_TYPE_ID = os.getenv("TRAIL_LEARNING_OBJECT_TYPE_ID", "d841e9d8-d669-4d88-9636-1072765d0738")


class LearningTrailProcessor(AbstractLearningObjectProcessor):
    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.learning_trail"

    def _transform_row(self, pk: str, row: Row, connection: Connection) -> dict:
        return {
            "source_id": pk,
            "learning_object_type_id": TRAIL_LEARNING_OBJECT_TYPE_ID,
            "deleted_date": self.str_date(row["deleted_date"]),
            "name": row["name"],
            "development_status": "",
        }

    def list_source_objects(self, ids: str, connection: Connection) -> ResultProxy:
        return self.db.run_sql(connection, "konquest/all_learning_trails_chunk.sql", ids=ids)
