import os
import typing
from abc import ABC, abstractmethod

from sqlalchemy.engine import Connection, ResultProxy, Row

from db import format_query_values, transaction_context
from indexer.processors import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class AbstractLearningObjectProcessor(AbstractProcessor, ABC):
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"

    @abstractmethod
    def get_kafka_main_topic(self) -> str:
        pass

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    def remove_deleted_instances(self, deleted_ids: set[str], delete_msgs: list[KeepsMessage] = None):
        with self.db.regulatory_compliance.connect() as conn:
            deleted_ids = ",".join(f"'{item}'" for item in deleted_ids)
            self.db.run_sql(conn, "regulatory_compliance/learning_objects_delete.sql", ids=deleted_ids)

    @abstractmethod
    def _transform_row(self, pk: str, row: Row, connection: Connection) -> dict:
        """Return a list of object to save"""

    @abstractmethod
    def list_source_objects(self, ids: str, connection: Connection) -> ResultProxy:
        pass

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        updated_ids = self.remove_nones(updated_ids)
        with self.db.konquest_engine.connect() as konquest_conn:
            objects_to_save = []

            in_ids = ",".join([f"'{id}'" for id in updated_ids])
            self.log("DEBUG - in_ids: " + str(in_ids))
            rs = self.list_source_objects(in_ids, konquest_conn)
            self.log("DEBUG - rs: " + str(rs))
            for i, row in enumerate(rs):
                self.log("DEBUG - row: " + str(rs))
                object_id = row["id"] if "id" in row else row["mission_id"]
                self.log("DEBUG - object: " + str(rs))
                object_id = self.str_id(object_id)
                self.log("DEBUG - object: " + str(rs))
                self.log(f"[{object_id}] -- Processing {i + 1} of {len(updated_ids)}")
                objects_to_save.append(self._transform_row(object_id, row, konquest_conn))

        with transaction_context(self.db.regulatory_compliance) as conn:
            for object_to_save in objects_to_save:
                self.log("DEBUG - object_to_save: " + str(object_to_save))
                object_to_save = format_query_values(object_to_save)
                self.db.run_sql(conn, "regulatory_compliance/learning_objects_save.sql", **object_to_save)

        return True
