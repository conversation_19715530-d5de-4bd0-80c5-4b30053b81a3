import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class AnswerRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-answers-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            "question",
            "exam",
            # TODO exam pode mudar de stage/mission ou pulse?
        ]
        return {f"{self.TOPIC_PREFIX}.{table}": None for table in tables}

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: list[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        # ignore deleted and first time read related messages
        updated_msgs = [msg for msg in batch if msg.op not in ["d", "r"]]

        exams = [exam for exam in updated_msgs if exam.topic == self.TOPIC_PREFIX + ".exam"]
        questions = [question for question in updated_msgs if question.topic == self.TOPIC_PREFIX + ".question"]

        self.log(f"-- {len(batch)} messages, {len(exams)} exams, {len(questions)} questions")

        for exam in exams:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"exam_id": exam.id}},
                    "script": {"lang": "painless", "source": 'ctx._source.exam_name = params["name"]', "params": {"name": exam.entity("title")}},
                },
            )

        for question in questions:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"question_id": question.id}},
                    "script": {
                        "lang": "painless",
                        "source": 'ctx._source.question_name = params["name"]',
                        "params": {"name": question.entity("exam_question")},
                    },
                },
            )

        return True
