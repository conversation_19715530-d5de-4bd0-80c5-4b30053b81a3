import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class EnrollmentRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-enrollments-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"
    USER_TOPIC = f"pg_myaccount_{ENV_SUFFIX}.public.user"

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            "mission",
            "mission_category",
        ]
        topics = {f"{self.TOPIC_PREFIX}.{table}": None for table in tables}
        topics[self.USER_TOPIC] = None
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: list[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        # ignore deleted and first time read related messages
        updated_msgs = [msg for msg in batch if msg.op not in ["d", "r"]]

        missions = [mission for mission in updated_msgs if mission.topic == self.TOPIC_PREFIX + ".mission"]
        categories = [category for category in updated_msgs if category.topic == self.TOPIC_PREFIX + ".mission_category"]

        users = [user for user in updated_msgs if user.topic == self.USER_TOPIC]

        self.log(f"-- {len(batch)} messages, {len(users)} users, {len(missions)} mission, {len(categories)} categories")

        for mission in missions:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"course_id": mission.id}},
                    "script": {"lang": "painless", "source": 'ctx._source.course_name = params["name"]', "params": {"name": mission.entity("name")}},
                },
            )

        for category in categories:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {"term": {"category_id": category.id}},
                    "script": {"lang": "painless", "source": 'ctx._source.category_name = params["name"]', "params": {"name": category.entity("name")}},
                },
            )

        for user in users:
            q = {
                "query": {"term": {"user_id": user.id}},
                "script": {
                    "lang": "painless",
                    "source": """
                        ctx._source.user_name = params['name'];
                        ctx._source.user_avatar = params['avatar'];
                        """.replace("\n", ""),
                    "params": {
                        "name": user.entity("name"),
                        "avatar": user.entity("avatar"),
                    },
                },
            }
            # print(f'--- query: {q}')
            self.es.es_client.update_by_query(index=self.ELASTIC_INDEX, conflicts="proceed", body=q)

        return True
