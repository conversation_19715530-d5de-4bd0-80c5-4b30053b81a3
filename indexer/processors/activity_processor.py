import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class ActivityProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-activities-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.learn_content_activity"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            "properties": {
                "id": {"type": "keyword"},
                "action": {"type": "keyword"},
                "created_date": {"type": "date"},
                "start_date": {"type": "date"},
                "stop_date": {"type": "date"},
                "time_in": {
                    "type": "scaled_float",
                    "scaling_factor": 1000,
                },
                "user_id": {"type": "keyword"},
                "pulse_id": {"type": "keyword"},
                "course_id": {"type": "keyword"},
                "stage_id": {"type": "keyword"},
                "stage_content_id": {"type": "keyword"},
                "enrollment_id": {"type": "keyword"},
                "kontent_id": {"type": "keyword"},
                "content_type_id": {"type": "keyword"},
                "content_type_name": {"type": "keyword"},
                "workspace_id": {"type": "keyword"},
            }
        }

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        with self.db.konquest_engine.connect() as konquest_conn, self.db.kontent_engine.connect() as kontent_conn:
            docs = []

            in_ids = ",".join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, "konquest/activities_chunk.sql", activities_ids=in_ids)

            for i, row in enumerate(rs):
                activity_id = self.str_id(row["id"])
                self.log(f"[{activity_id}] -- Processing {i + 1} of {len(updated_ids)}")

                activity = {
                    "_id": activity_id,  # elastic index id
                    "id": activity_id,
                    "action": row["action"],
                    "created_date": self.str_date(row["created_date"]),
                    "start_date": self.str_date(row["time_start"]),
                    "stop_date": self.str_date(row["time_stop"]),
                    "time_in": row["time_in"].total_seconds() if row["time_in"] else None,
                    "user_id": self.str_id(row["user_id"]),
                    "pulse_id": self.str_id(row["pulse_id"]),
                    "course_id": self.str_id(row["mission_id"]),
                    "stage_id": self.str_id(row["stage_id"]),
                    "stage_content_id": self.str_id(row["mission_stage_content_id"]),
                    "enrollment_id": self.str_id(row["mission_enrollment_id"]),
                    "kontent_id": self.str_id(row["kontent_id"]),
                    "workspace_id": row["workspace_id"],
                }

                docs.append(activity)

            self._populate_content_types(kontent_conn, docs)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True

    def _populate_content_types(self, kontent_conn, activities):
        kontent_ids = []
        activities_map = {}
        for activity in activities:
            if not activity["kontent_id"]:  # pulse
                continue
            kontent_id = activity["kontent_id"]
            kontent_ids.append(kontent_id)
            activities_map[kontent_id] = activity

        if not kontent_ids:
            return None

        in_ids = ",".join([f"'{id}'" for id in kontent_ids])
        rs = self.db.run_sql(kontent_conn, "kontent/content_types_for_contents.sql", kontent_ids=in_ids)

        for row in rs:
            kontent_id = self.str_id(row["learn_content_id"])
            activity = activities_map[kontent_id]
            activity.update({"content_type_id": self.str_id(row["content_type_id"]), "content_type_name": row["content_type_name"]})

        return activities
