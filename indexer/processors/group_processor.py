import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class GroupProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-groups-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_konquest_{ENV_SUFFIX}.public"

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.group"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return {
            f"{self.TOPIC_PREFIX}.group_mission": lambda msg: msg.entity("group_id"),
            f"{self.TOPIC_PREFIX}.group_channel": lambda msg: msg.entity("group_id"),
            f"{self.TOPIC_PREFIX}.group_learning_trail": lambda msg: msg.entity("group_id"),
            f"{self.TOPIC_PREFIX}.group_user": lambda msg: msg.entity("group_id"),
        }

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            "properties": {
                "id": {"type": "keyword"},
                "name": {"type": "keyword"},
                "created_date": {"type": "date"},
                "updated_date": {"type": "date"},
                "workspace_id": {"type": "keyword"},
                "course_id": {"type": "keyword"},  # array
                "channel_id": {"type": "keyword"},  # array
                "learning_trail_id": {"type": "keyword"},  # array
                "user_id": {"type": "keyword"},  # array
            }
        }

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        with self.db.konquest_engine.connect() as konquest_conn:
            docs = []

            in_ids = ",".join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, "konquest/groups_chunk.sql", groups_ids=in_ids)

            for i, row in enumerate(rs):
                group_id = self.str_id(row["group_id"])
                self.log(f"[{group_id}] -- Processing {i + 1} of {len(updated_ids)}")

                group = {
                    "_id": group_id,  # elastic index id
                    "id": group_id,
                    "name": row["name"],
                    "created_date": self.str_date(row["created_date"]),
                    "updated_date": self.str_date(row["updated_date"]),
                    "workspace_id": row["workspace_id"],
                    "course_id": row["missions"],
                    "channel_id": row["channels"],
                    "learning_trail_id": row["learning_trails"],
                    "user_id": row["users"],
                }
                docs.append(group)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True
