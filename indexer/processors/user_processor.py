import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class UserProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-users-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_myaccount_{ENV_SUFFIX}.public"
    ENROLLMENT_TOPIC = f"pg_konquest_{ENV_SUFFIX}.public.mission_enrollment"

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.user"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return {
            f"{self.TOPIC_PREFIX}.user_role_workspace": lambda msg: msg.entity("user_id"),
            f"{self.TOPIC_PREFIX}.user_profile_workspace": lambda msg: msg.entity("user_id"),
            f"{self.ENROLLMENT_TOPIC}": lambda msg: msg.entity("user_id"),
        }

    def get_index_name(self) -> str:
        return self.ELASTIC_INDEX

    def get_index_mappings(self) -> str:
        return {
            "properties": {
                "id": {"type": "keyword"},
                "name": {
                    "type": "text",
                    "copy_to": "search_terms",
                    "fields": {
                        "keyword": {"type": "keyword"},
                        "sortable": {"type": "keyword", "normalizer": "case_insensitive"},
                    },
                },
                "nickname": {"type": "text", "copy_to": "search_terms", "fields": {"keyword": {"type": "keyword"}}},
                "email": {"type": "keyword"},
                "secondary_email": {"type": "keyword"},
                "phone": {"type": "keyword"},
                "gender": {"type": "keyword"},
                "birthday": {"type": "date"},
                "address": {"type": "keyword"},
                "avatar": {"type": "keyword"},
                "language_id": {"type": "keyword"},
                "leader_id": {"type": "keyword"},
                "leader_name": {"type": "keyword"},
                "status": {"type": "keyword"},
                "origin": {"type": "keyword"},
                "created_date": {"type": "date"},
                "updated_date": {"type": "date"},
                "search_terms": {
                    "type": "search_as_you_type",
                    "analyzer": "folding",
                },
                "user_role_workspace": {
                    "type": "nested",
                    "properties": {
                        "id": {"type": "keyword"},
                        "workspace_id": {"type": "keyword"},
                        "role": {
                            "type": "object",
                            "properties": {
                                "id": {"type": "keyword"},
                                "application_id": {"type": "keyword"},
                                "name": {"type": "keyword"},
                                "key": {"type": "keyword"},
                            },
                        },
                    },
                },
                "user_profile_workspace": {
                    "type": "nested",
                    "properties": {
                        "id": {"type": "keyword"},
                        "workspace_id": {"type": "keyword"},
                        "area_of_activity": {"type": "keyword"},
                        "manager": {"type": "keyword"},
                        "director": {"type": "keyword"},
                        "job_id": {"type": "keyword"},
                        "job_name": {"type": "keyword"},
                        "job_function_id": {"type": "keyword"},
                        "job_function_name": {"type": "keyword"},
                    },
                },
                "user_progress_workspace": {
                    "type": "nested",
                    "properties": {
                        "workspace_id": {"type": "keyword"},
                        "enrollments_total": {"type": "integer"},
                        "enrollments_completed": {"type": "integer"},
                        "enrollments_completed_ratio": {
                            "type": "scaled_float",
                            "scaling_factor": 100,
                        },
                    },
                },
            }
        }

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        with self.db.myaccount_engine.connect() as myaccount_conn, self.db.konquest_engine.connect() as konquest_conn:
            docs = []

            in_ids = ",".join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(myaccount_conn, "myaccount/users_chunk.sql", users_ids=in_ids)

            for i, row in enumerate(rs):
                user_id = self.str_id(row["id"])
                self.log(f"[{user_id}] -- Processing {i + 1} of {len(updated_ids)}")

                user_role_workspaces = self._query_user_role_workspaces(myaccount_conn, user_id)

                user_profile_workspaces = self._query_user_profile_workspaces(myaccount_conn, user_id)

                user_progress_workspaces = self._query_user_progress_workspaces(konquest_conn, user_id)

                user = {
                    "_id": user_id,  # elastic index id
                    "id": user_id,
                    "name": row["name"],
                    "nickname": row["nickname"],
                    "email": row["email"],
                    "secondary_email": row["secondary_email"],
                    "phone": row["phone"],
                    "gender": row["gender"],
                    "birthday": row["birthday"],
                    "address": row["address"],
                    "avatar": row["avatar"],
                    "language_id": row["language_id"],
                    "leader_id": row["leader_id"],
                    "leader_name": row["leader_name"],
                    "status": row["status"],
                    "origin": "myaccount",
                    "created_date": self.str_date(row["created_date"]),
                    "updated_date": self.str_date(row["updated_date"]),
                    "user_role_workspace": user_role_workspaces,
                    "user_profile_workspace": user_profile_workspaces,
                    "user_progress_workspace": user_progress_workspaces,
                }
                docs.append(user)

            self.es.bulk_save(self.ELASTIC_INDEX, docs)

        return True

    def _query_user_role_workspaces(self, conn, user_id):
        user_role_workspaces = []

        rs = self.db.run_sql(conn, "myaccount/user_role_workspace.sql", user_id=user_id)
        for row in rs:
            user_role_workspaces.append(
                {
                    "id": self.str_id(row["id"]),
                    "workspace_id": self.str_id(row["workspace_id"]),
                    "role": {
                        "id": self.str_id(row["role_id"]),
                        "application_id": row["application_id"],
                        "name": row["role_name"],
                        "key": row["role_key"],
                    },
                }
            )
        return user_role_workspaces

    def _query_user_profile_workspaces(self, conn, user_id):
        user_profile_workspaces = []

        rs = self.db.run_sql(conn, "myaccount/user_profile_workspace.sql", user_id=user_id)
        for row in rs:
            user_profile_workspaces.append(
                {
                    "id": self.str_id(row["id"]),
                    "workspace_id": self.str_id(row["workspace_id"]),
                    "area_of_activity": row["area_of_activity"],
                    "manager": row["manager"],
                    "director": row["director"],
                    "job_id": self.str_id(row["job_id"]),
                    "job_name": row["job_name"],
                    "job_function_id": self.str_id(row["job_function_id"]),
                    "job_function_name": row["job_function_name"],
                }
            )
        return user_profile_workspaces

    def _query_user_progress_workspaces(self, conn, user_id):
        user_progress_workspaces = []

        rs = self.db.run_sql(conn, "konquest/user_progress_workspaces.sql", user_id=user_id)
        for row in rs:
            total = row["enrollments_total"]
            completed = row["enrollments_completed"]
            user_progress_workspaces.append(
                {
                    "workspace_id": self.str_id(row["workspace_id"]),
                    "enrollments_total": total,
                    "enrollments_completed": completed,
                    "enrollments_completed_ratio": completed / total if total else 0,
                }
            )

        return user_progress_workspaces
