import os
import typing

from . import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class UserRelationsProcessor(AbstractProcessor):
    ELASTIC_INDEX = f"kafka-analytics-users-{ENV_SUFFIX}"
    TOPIC_PREFIX = f"pg_myaccount_{ENV_SUFFIX}.public"

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            "job",
            "job_function",
        ]
        return {f"{self.TOPIC_PREFIX}.{table}": None for table in tables}

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    # Skip pre-processing for relations
    def pre_process_batch(self, batch: list[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        # ignore deleted and first time read related messages
        updated_msgs = [msg for msg in batch if msg.op not in ["d", "r"]]

        jobs = [job for job in updated_msgs if job.topic == self.TOPIC_PREFIX + ".job"]
        job_functions = [function for function in updated_msgs if function.topic == self.TOPIC_PREFIX + ".job_function"]

        self.log(f"-- {len(batch)} messages, {len(jobs)} jobs, {len(job_functions)} job functions")

        for job in jobs:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {
                        "nested": {"path": "user_profile_workspace", "query": {"bool": {"filter": [{"term": {"user_profile_workspace.job_id": job.id}}]}}},
                    },
                    "script": {
                        "lang": "painless",
                        "source": """
                        def targets = ctx._source.user_profile_workspace.findAll(upw -> upw.job_id == params['job_id']);
                        for(upw in targets) {
                            upw.job_name = params['job_name']
                        }
                        """.replace("\n", ""),
                        "params": {"job_id": job.id, "job_name": job.entity("name")},
                    },
                },
            )

        for function in job_functions:
            self.es.es_client.update_by_query(
                index=self.ELASTIC_INDEX,
                conflicts="proceed",
                body={
                    "query": {
                        "nested": {
                            "path": "user_profile_workspace",
                            "query": {"bool": {"filter": [{"term": {"user_profile_workspace.job_function_id": function.id}}]}},
                        },
                    },
                    "script": {
                        "lang": "painless",
                        "source": """
                        def targets = ctx._source.user_profile_workspace.findAll(upw -> upw.job_function_id == params['job_function_id']);
                        for(upw in targets) {
                            upw.job_function_name = params['job_function_name']
                        }
                        """.replace("\n", ""),
                        "params": {"job_function_id": function.id, "job_function_name": function.entity("name")},
                    },
                },
            )

        return True
