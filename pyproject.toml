[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Migrated from .flake8 configuration
line-length = 160

# Assume Python 3.12+.
target-version = "py312"

[tool.ruff.lint]
# Enable comprehensive linting rules
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # Pyflakes
    "I",    # isort
    #"N",    # pep8-naming
    "UP",   # pyupgrade
    #"B",    # flake8-bugbear
    #"C4",   # flake8-comprehensions
    "COM",  # flake8-commas
    #"SIM",  # flake8-simplify
    "PIE",  # flake8-pie
    "RET",  # flake8-return
    "ARG",  # flake8-unused-arguments
    #"PTH",  # flake8-use-pathlib
    "ERA",  # eradicate (commented code)
    #"PL",   # Pylint
    #"RUF",  # Ruff-specific rules
]

# Ignore specific rules that may be too strict for this project
ignore = [
    "COM812",   # trailing-comma-missing (conflicts with Ruff formatter)
    "COM819",   # trailing-comma-prohibited (conflicts with Ruff formatter)
    "E201",     # whitespace after '(' (migrated from .flake8)
    "E202",     # whitespace before ')' (migrated from .flake8)
    "N806",     # variable in function should be lowercase (conflicts with SQL naming)
    "N803",     # argument name should be lowercase (conflicts with SQL naming)
    "PLR0913",  # too many arguments (common in data processing)
    "PLR0915",  # too many statements (common in processors)
    "PLR2004",  # magic value used in comparison (common with status codes)
    "ARG002",   # unused method argument (common in abstract methods)
    "PTH123",   # use pathlib instead of os.path.join (gradual migration)
    "ERA001",   # commented-out code (may have legitimate TODO comments)
    "RET504",   # unnecessary variable assignment before return
    "SIM108",   # use ternary operator (readability preference)
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Pylint configuration
[tool.ruff.lint.pylint]
max-args = 8
max-branches = 15
max-returns = 8
max-statements = 60

# isort configuration
[tool.ruff.lint.isort]
known-first-party = ["indexer", "db", "es", "config"]
force-single-line = false
lines-after-imports = 2

[tool.ruff.format]
# Use double quotes for strings.
quote-style = "double"

# Use spaces around the equals sign in keyword arguments.
skip-magic-trailing-comma = false

# Indent with spaces, rather than tabs.
indent-style = "space"

# Respect magic trailing commas.
docstring-code-format = false
