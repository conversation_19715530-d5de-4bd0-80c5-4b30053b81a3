# SonarQube Configuration for Media Convert Microservice
sonar.projectKey=kafka-data-indexer
sonar.projectName=Kafka Data Indexer
sonar.projectVersion=1.0.0

# Source code and tests
sonar.sources=.
#sonar.tests=tests
sonar.sourceEncoding=UTF-8
sonar.python.version=3.12
sonar.language=py

# Coverage and test reports (Community Edition compatible)
#sonar.python.coverage.reportPaths=.reports/coverage.xml

sonar.python.ruff.reportPaths=.reports/ruff-report.json
sonar.exclusions=**/migrations/**,**/docker/**,**/scripts/**,**/__pycache__/**,**/temp/**,**/logs/**,**/.venv/**,**/.pytest_cache/**,**/.mypy_cache/**,**/.ruff_cache/**
sonar.coverage.exclusions=**/migrations/**,**/docker/**,**/scripts/**,**/__init__.py,**/conftest.py,**/test_*.py
sonar.cpd.exclusions=**/__init__.py
sonar.qualitygate.wait=true
sonar.verbose=false
sonar.log.level=INFO