# Elasticsearch engine
import os
import typing

from elasticsearch.client import Elasticsearch
from elasticsearch.helpers import bulk

from indexer.logging_config import get_logger


logger = get_logger("elasticsearch")

SERVER = os.getenv("ELASTICSEARCH_SERVER")
USER = os.getenv("ELASTICSEARCH_USER", "elastic")
PASS = os.getenv("ELASTICSEARCH_PASSWORD")
es_client = Elasticsearch(SERVER, basic_auth=(USER, PASS), max_retries=3, retry_on_timeout=True)


def bulk_delete(index: str, ids: typing.Iterable[str]):
    return es_client.delete_by_query(index=index, conflicts="proceed", body={"query": {"terms": {"id": list(ids)}}})


def bulk_save(index: str, docs: typing.Iterable[dict]):
    for doc in docs:
        doc.update({"_index": index})
    return bulk(es_client, docs)


def create_or_update_index(index: str, settings: any = {}, mappings: any = {}):
    if not es_client.indices.exists(index=index):
        logger.info(f"Creating new index: [{index}]")
        body = {
            "settings": settings,
            "mappings": mappings,
        }
        return es_client.indices.create(index=index, body=body)

    logger.info(f"Updating existing index: [{index}]")
    es_client.indices.close(index=index)
    updateable_settings = {k: v for k, v in settings.items() if k not in ["number_of_shards", "number_of_replicas"]}
    if updateable_settings:
        es_client.indices.put_settings(index=index, body=updateable_settings)
    result = es_client.indices.put_mapping(index=index, body=mappings)
    es_client.indices.open(index=index)
    return result
